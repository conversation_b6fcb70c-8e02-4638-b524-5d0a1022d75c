@echo off
chcp 65001 >nul
title 重启电厂智慧燃烧系统
set "exeName=SmartBurning.exe"

echo [%time%] 正在重启程序...
echo [%time%] 当前目录: %cd%
echo [%time%] 可执行文件: %exeName%
echo [%time%] 完整路径: %cd%\%exeName%

:: 检查文件是否存在
if exist "%cd%\%exeName%" (
    echo [%time%] 文件存在，继续执行...
) else (
    echo [%time%] 错误：找不到文件 %cd%\%exeName%
    pause
    exit /b 1
)

echo [%time%] 结束现有进程...
taskkill /f /im "%exeName%" 2>nul
if %errorlevel% equ 0 (
    echo [%time%] 进程已终止
) else (
    echo [%time%] 没有找到运行中的进程或终止失败
)

timeout /t 2 /nobreak >nul

echo [%time%] 启动新进程...
start "" "%cd%\%exeName%"
if %errorlevel% equ 0 (
    echo [%time%] 进程启动成功
) else (
    echo [%time%] 进程启动失败，错误代码: %errorlevel%
    pause
    exit /b 1
)

echo [%time%] 重启完成
timeout /t 3 /nobreak >nul
